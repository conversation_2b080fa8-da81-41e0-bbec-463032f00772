import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Token Generator',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const TokenGeneratorScreen(),
    );
  }
}

class TokenGeneratorScreen extends StatefulWidget {
  const TokenGeneratorScreen({super.key});

  @override
  State<TokenGeneratorScreen> createState() => _TokenGeneratorScreenState();
}

class _TokenGeneratorScreenState extends State<TokenGeneratorScreen> {
  String? _currentToken;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSavedToken();
  }

  // Load token from SharedPreferences when app starts
  Future<void> _loadSavedToken() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _currentToken = prefs.getString('user_token');
    });
  }

  // Generate a random token
  String _generateToken() {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return List.generate(
      32,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  // Generate and save token to SharedPreferences
  Future<void> _generateAndSaveToken() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate some processing time
    await Future.delayed(const Duration(milliseconds: 500));

    final newToken = _generateToken();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_token', newToken);

    setState(() {
      _currentToken = newToken;
      _isLoading = false;
    });

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Token generated and saved successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Token Generator'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.security, size: 80, color: Colors.deepPurple),
            const SizedBox(height: 30),
            const Text(
              'Token Generator App',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: _isLoading ? null : _generateAndSaveToken,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 40,
                  vertical: 15,
                ),
                textStyle: const TextStyle(fontSize: 18),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Generate Token'),
            ),
            const SizedBox(height: 40),
            const Text(
              'Current Token:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 15),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _currentToken ?? 'No token generated yet',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'monospace',
                  color: _currentToken != null
                      ? Colors.black87
                      : Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            if (_currentToken != null) ...[
              const SizedBox(height: 20),
              Text(
                'Token saved in SharedPreferences',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
